<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";

  import type { ShellConfig } from "@augment-internal/sidecar-libs/src/tools/tool-types";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextAreaAugment from "$common-webviews/src/design-system/components/TextAreaAugment.svelte";

  export let supportedShells: ShellConfig[] = [];
  export let selectedShell: string | undefined = undefined;
  export let startupScript: string | undefined = undefined;
  export let onShellSelect: (shell: string) => void;
  export let onStartupScriptChange: (script: string) => void;

  function findShellByFriendlyName(friendlyName: string): ShellConfig | undefined {
    return supportedShells.find((s) => s.friendlyName === friendlyName);
  }

  // Reference to the dropdown close function
  let requestClose: () => void;

  // Handle startup script changes
  function handleStartupScriptChange(event: Event) {
    const target = event.target as HTMLTextAreaElement;
    onStartupScriptChange(target.value);
  }
  $: shellConfig = selectedShell ? findShellByFriendlyName(selectedShell) : undefined;
</script>

<div class="terminal-settings">
  <TextAugment size={1} weight="regular" color="secondary">
    <div class="section-heading-text">Terminal</div>
  </TextAugment>

  <div class="shell-selector">
    <TextAugment size={1}>Shell:</TextAugment>
    <DropdownMenuAugment.Root bind:requestClose>
      <DropdownMenuAugment.Trigger>
        <ButtonAugment
          size={1}
          variant="outline"
          color="neutral"
          disabled={supportedShells.length === 0}
        >
          {#if shellConfig && supportedShells.length > 0}
            {shellConfig.friendlyName}
            ({shellConfig.supportString})
          {:else if supportedShells.length === 0}
            No shells available
          {:else}
            Select a shell
          {/if}
          <ChevronDown slot="iconRight" />
        </ButtonAugment>
      </DropdownMenuAugment.Trigger>
      <DropdownMenuAugment.Content side="bottom" align="start">
        {#if supportedShells.length > 0}
          {#each supportedShells as shell (shell.friendlyName)}
            <DropdownMenuAugment.Item
              onSelect={() => {
                onShellSelect(shell.friendlyName);
                requestClose();
              }}
              highlight={selectedShell === shell.friendlyName}
            >
              {shell.friendlyName}
              ({shell.supportString})
            </DropdownMenuAugment.Item>
          {/each}
        {:else}
          <DropdownMenuAugment.Label>No shells available</DropdownMenuAugment.Label>
        {/if}
      </DropdownMenuAugment.Content>
    </DropdownMenuAugment.Root>
  </div>

  <div class="startup-script-container">
    <TextAugment size={1}
      >Start-up script: Code to run wherever a new terminal is opened</TextAugment
    >
    <TextAreaAugment
      bind:value={startupScript}
      on:change={handleStartupScriptChange}
      placeholder="Enter shell commands to run on terminal startup"
      resize="vertical"
    />
  </div>
</div>

<style>
  .terminal-settings {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .shell-selector {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-3);
  }

  .shell-selector :global([data-dropdown-root]) {
    flex: 1;
  }

  .shell-selector :global(.c-base-btn),
  .shell-selector :global(.c-button--content) {
    min-width: 250px;
    width: 100%;
    justify-content: space-between;
    background-color: var(--ds-surface);
  }

  .startup-script-container {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }
</style>
